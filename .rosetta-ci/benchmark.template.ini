#
# Benchmark script configuration file. Some of the tests require some system specific options to run. Please see benchmark.ini.template for list of available options.
#

[DEFAULT]

[main] # additional config-options for various tests. All this fields will be pass as keys in 'config' function argument

# how many jobs daemon can run on host machine (this is not related to HPC jobs)
cpu_count = 24

# how many memory in GB daemon can use on host machine (approximation, float)
memory = 64

# user name and email for user who submitted this test
user_name  = <PERSON>
user_email = <EMAIL>

# HPC Driver, might have one of the following values: MultiCore, Condor, Slurm or none if no HPC Driver should be configured
hpc_driver = MultiCore

# when running by daemons branch:revision will be set to appropriate values to represent currently checked version of main repository
branch = unknown
revision = 42

# path to directory where test results will be stored
results_root = ${_here_}/results

release_root = ./results/_release_

[slurm]
# head-node host name, if specified will be used to submit jobs
head_node =


[mount]
# list of key:path pairs that will be avalible as config.mounts during test run

# path to releases, leave empty if release production should not be supported by this daemon
release_root = ${_here_}/release
